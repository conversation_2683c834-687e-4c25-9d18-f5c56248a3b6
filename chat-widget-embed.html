<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Widget</title>
    <style>
        /* Chat Widget Styles */
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .chat-toggle {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #3b82f6;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .chat-toggle:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .chat-toggle svg {
            width: 24px;
            height: 24px;
            fill: white;
        }

        .chat-window {
            width: 320px;
            height: 400px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            display: none;
            flex-direction: column;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .chat-window.open {
            display: flex;
        }

        .chat-header {
            background: #3b82f6;
            color: white;
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-header-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-title {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
        }

        .chat-subtitle {
            font-size: 12px;
            opacity: 0.9;
            margin: 0;
        }

        .chat-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
        }

        .chat-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            background: #f9fafb;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .chat-message {
            display: flex;
            max-width: 80%;
        }

        .chat-message.user {
            align-self: flex-end;
        }

        .chat-message.assistant {
            align-self: flex-start;
        }

        .message-bubble {
            padding: 8px 12px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message-bubble.user {
            background: #3b82f6;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message-bubble.assistant {
            background: white;
            color: #374151;
            border: 1px solid #e5e7eb;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
        }

        .chat-input-area {
            padding: 16px;
            border-top: 1px solid #e5e7eb;
            background: white;
            display: flex;
            gap: 8px;
        }

        .chat-input {
            flex: 1;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            outline: none;
        }

        .chat-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .chat-send {
            background: #3b82f6;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-send:hover:not(:disabled) {
            background: #2563eb;
        }

        .chat-send:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            border-bottom-left-radius: 4px;
            font-size: 14px;
            color: #6b7280;
        }

        .typing-dots {
            display: flex;
            gap: 2px;
        }

        .typing-dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #6b7280;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .empty-state {
            text-align: center;
            color: #6b7280;
            padding: 32px 16px;
            font-size: 14px;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <!-- Example usage on a website -->
    <div style="padding: 40px; max-width: 800px; margin: 0 auto;">
        <h1>My Website</h1>
        <p>This is an example website with an embedded AI chat widget. The chat widget appears in the bottom-right corner.</p>
        <p>You can customize the agent ID, API key, colors, and position to match your needs.</p>
    </div>

    <!-- Chat Widget -->
    <div id="chat-widget" class="chat-widget">
        <div id="chat-window" class="chat-window">
            <div class="chat-header">
                <div class="chat-header-info">
                    <div class="chat-avatar">
                        <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21Z"/>
                        </svg>
                    </div>
                    <div>
                        <p class="chat-title">AI Assistant</p>
                        <p class="chat-subtitle">How can I help you today?</p>
                    </div>
                </div>
                <button id="chat-close" class="chat-close">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            
            <div id="chat-messages" class="chat-messages">
                <div class="empty-state">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor" style="margin: 0 auto 8px; opacity: 0.5;">
                        <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
                    </svg>
                    <p>Start a conversation!</p>
                </div>
            </div>
            
            <div class="chat-input-area">
                <input id="chat-input" class="chat-input" type="text" placeholder="Type your message..." />
                <button id="chat-send" class="chat-send">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                    </svg>
                </button>
            </div>
        </div>
        
        <button id="chat-toggle" class="chat-toggle">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
        </button>
    </div>

    <script>
        // Configuration - Replace these with your actual values
        const CHAT_CONFIG = {
            agentId: '1112efb4-5d41-4fc0-ae39-809ae18a163f', // Replace with your agent ID
            apiKey: 'sk-YOUR_API_KEY', // Replace with your API key
            serverUrl: 'http://localhost:9094', // Replace with your server URL
            title: 'AI Assistant',
            subtitle: 'How can I help you today?',
            primaryColor: '#3b82f6'
        };

        // Chat Widget Implementation
        class ChatWidget {
            constructor(config) {
                this.config = config;
                this.messages = [];
                this.isOpen = false;
                this.isLoading = false;
                
                this.initializeElements();
                this.attachEventListeners();
            }

            initializeElements() {
                this.chatWindow = document.getElementById('chat-window');
                this.chatToggle = document.getElementById('chat-toggle');
                this.chatClose = document.getElementById('chat-close');
                this.chatMessages = document.getElementById('chat-messages');
                this.chatInput = document.getElementById('chat-input');
                this.chatSend = document.getElementById('chat-send');
            }

            attachEventListeners() {
                this.chatToggle.addEventListener('click', () => this.toggleChat());
                this.chatClose.addEventListener('click', () => this.closeChat());
                this.chatSend.addEventListener('click', () => this.sendMessage());
                this.chatInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }

            toggleChat() {
                this.isOpen = !this.isOpen;
                this.chatWindow.classList.toggle('open', this.isOpen);
                if (this.isOpen) {
                    this.chatInput.focus();
                }
            }

            closeChat() {
                this.isOpen = false;
                this.chatWindow.classList.remove('open');
            }

            async sendMessage() {
                const message = this.chatInput.value.trim();
                if (!message || this.isLoading) return;

                this.addMessage('user', message);
                this.chatInput.value = '';
                this.setLoading(true);

                try {
                    const response = await fetch(`${this.config.serverUrl}/api/v1/agents/${this.config.agentId}/chat`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.config.apiKey}`,
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            sessionId: `widget-${Date.now()}`,
                        }),
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
                    }

                    const data = await response.json();
                    
                    // Extract assistant response
                    let assistantContent = 'I received your message but couldn\'t generate a response.';
                    if (data.response && data.response.messages) {
                        const lastMessage = data.response.messages[data.response.messages.length - 1];
                        if (lastMessage && lastMessage.content) {
                            assistantContent = lastMessage.content;
                        }
                    } else if (data.response && typeof data.response === 'string') {
                        assistantContent = data.response;
                    }

                    this.addMessage('assistant', assistantContent);
                } catch (error) {
                    this.showError(error.message);
                } finally {
                    this.setLoading(false);
                }
            }

            addMessage(role, content) {
                const message = {
                    id: Date.now().toString(),
                    role,
                    content,
                    timestamp: new Date()
                };

                this.messages.push(message);
                this.renderMessages();
            }

            setLoading(loading) {
                this.isLoading = loading;
                this.chatSend.disabled = loading;
                this.chatInput.disabled = loading;
                
                if (loading) {
                    this.showTypingIndicator();
                } else {
                    this.hideTypingIndicator();
                }
            }

            showTypingIndicator() {
                const indicator = document.createElement('div');
                indicator.id = 'typing-indicator';
                indicator.className = 'typing-indicator';
                indicator.innerHTML = `
                    <span>AI is typing</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                `;
                this.chatMessages.appendChild(indicator);
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                const indicator = document.getElementById('typing-indicator');
                if (indicator) {
                    indicator.remove();
                }
            }

            showError(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = `Error: ${message}`;
                this.chatMessages.appendChild(errorDiv);
                this.scrollToBottom();
            }

            renderMessages() {
                // Clear messages except error messages
                const errors = this.chatMessages.querySelectorAll('.error-message');
                this.chatMessages.innerHTML = '';
                errors.forEach(error => this.chatMessages.appendChild(error));

                if (this.messages.length === 0) {
                    const emptyState = document.createElement('div');
                    emptyState.className = 'empty-state';
                    emptyState.innerHTML = `
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor" style="margin: 0 auto 8px; opacity: 0.5;">
                            <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
                        </svg>
                        <p>Start a conversation!</p>
                    `;
                    this.chatMessages.appendChild(emptyState);
                    return;
                }

                this.messages.forEach(message => {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `chat-message ${message.role}`;
                    
                    const bubble = document.createElement('div');
                    bubble.className = `message-bubble ${message.role}`;
                    
                    const content = document.createElement('p');
                    content.textContent = message.content;
                    content.style.margin = '0';
                    content.style.whiteSpace = 'pre-wrap';
                    
                    const time = document.createElement('p');
                    time.className = 'message-time';
                    time.textContent = message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    
                    bubble.appendChild(content);
                    bubble.appendChild(time);
                    messageDiv.appendChild(bubble);
                    this.chatMessages.appendChild(messageDiv);
                });

                this.scrollToBottom();
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }
        }

        // Initialize the chat widget when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ChatWidget(CHAT_CONFIG);
        });
    </script>
</body>
</html>
