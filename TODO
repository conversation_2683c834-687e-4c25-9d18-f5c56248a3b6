sk-E88C60EFE6


# Response will include: {"id": "task-uuid-here", ...}

# Step 2: Send message to the task
curl -X POST "http://localhost:9094/agents/1112efb4-5d41-4fc0-ae39-809ae18a163f/tasks/fd67a23f-d86b-4995-acf6-d3f420aa12e5/message" \
  -H "Authorization: Bearer sk-15188BE4E7" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{
      "role": "user",
      "content": "Hello! Can you help me?"
    }]
  }'


  
-----

curl -X GET "http://localhost:9094/api/v1/agents/test" \
  -H "Authorization: Bearer sk-15188BE4E7
"


# Simple chat endpoint (auto-creates task)
curl -X POST "http://localhost:9094/api/v1/agents/1112efb4-5d41-4fc0-ae39-809ae18a163f/chat" \
  -H "Authorization: Bearer sk-15188BE4E7" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello! Can you help me?",
    "sessionId": "test-session"
  }'