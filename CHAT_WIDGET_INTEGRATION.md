# 🤖 AI Chat Widget Integration Guide

## 📋 Overview

This chat widget allows you to embed your AI agent into any website with just a few lines of code. Users can chat with your agent directly from your website.

## 🚀 Quick Start

### Option 1: Standalone HTML File

1. **Download** the `chat-widget-embed.html` file
2. **Edit the configuration** at the top of the file:

```javascript
const CHAT_CONFIG = {
    agentId: 'YOUR_AGENT_ID',           // Replace with your agent ID
    apiKey: 'sk-YOUR_API_KEY',          // Replace with your API key  
    serverUrl: 'http://localhost:9094', // Replace with your server URL
    title: 'AI Assistant',              // Customize the title
    subtitle: 'How can I help you?',    // Customize the subtitle
    primaryColor: '#3b82f6'             // Customize the color
};
```

3. **Upload** the file to your website
4. **Done!** The chat widget will appear in the bottom-right corner

### Option 2: Embed in Existing Website

Add this code to your website's HTML:

```html
<!-- Add this CSS to your <head> section -->
<link rel="stylesheet" href="path/to/chat-widget.css">

<!-- Add this HTML before closing </body> tag -->
<div id="chat-widget-container"></div>

<!-- Add this JavaScript -->
<script src="path/to/chat-widget.js"></script>
<script>
    new ChatWidget({
        agentId: 'YOUR_AGENT_ID',
        apiKey: 'sk-YOUR_API_KEY',
        serverUrl: 'http://localhost:9094',
        title: 'AI Assistant',
        subtitle: 'How can I help you?',
        primaryColor: '#3b82f6'
    });
</script>
```

## ⚙️ Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `agentId` | string | **Required** | Your agent's unique ID |
| `apiKey` | string | **Required** | Your agent's API key |
| `serverUrl` | string | `http://localhost:9094` | Your server URL |
| `title` | string | `AI Assistant` | Widget header title |
| `subtitle` | string | `How can I help you?` | Widget header subtitle |
| `primaryColor` | string | `#3b82f6` | Widget theme color |
| `position` | string | `bottom-right` | Widget position (`bottom-right` or `bottom-left`) |

## 🎨 Customization

### Colors
```javascript
const config = {
    // ... other options
    primaryColor: '#ff6b6b',  // Custom brand color
};
```

### Position
```javascript
const config = {
    // ... other options
    position: 'bottom-left',  // Move to bottom-left corner
};
```

### Styling
You can override the CSS classes to match your website's design:

```css
.chat-widget {
    /* Custom positioning */
}

.chat-window {
    /* Custom window styling */
}

.chat-header {
    /* Custom header styling */
}
```

## 🔧 Getting Your Configuration

### 1. Get Your Agent ID
- Go to your agent's page
- Copy the ID from the URL: `/agents/{AGENT_ID}`

### 2. Generate API Key
- Go to your agent's chat page
- Click the configure dropdown (⚙️ icon)
- Click "Generate API Key"
- Copy the generated key (starts with `sk-`)

### 3. Set Server URL
- For local development: `http://localhost:9094`
- For production: `https://your-domain.com`

## 📱 Features

- ✅ **Responsive Design** - Works on desktop and mobile
- ✅ **Real-time Chat** - Instant responses from your AI agent
- ✅ **Session Management** - Maintains conversation context
- ✅ **Error Handling** - Graceful error messages
- ✅ **Typing Indicators** - Shows when AI is responding
- ✅ **Customizable** - Match your brand colors and style
- ✅ **Easy Integration** - Just a few lines of code

## 🔒 Security Notes

- **API Key**: Keep your API key secure and don't expose it in client-side code for production
- **CORS**: Make sure your server allows requests from your website domain
- **Rate Limiting**: Consider implementing rate limiting for public-facing widgets

## 🐛 Troubleshooting

### Common Issues

**1. 401 Unauthorized Error**
- Check that your API key is correct and active
- Ensure the API key belongs to the specified agent

**2. Connection Refused**
- Verify your server URL is correct and accessible
- Check that your server is running

**3. CORS Errors**
- Configure your server to allow requests from your website domain

**4. Widget Not Appearing**
- Check browser console for JavaScript errors
- Verify all configuration values are set correctly

### Debug Mode
Add this to enable console logging:

```javascript
const config = {
    // ... other options
    debug: true,  // Enable debug logging
};
```

## 🌐 Production Deployment

For production use:

1. **Secure API Keys**: Use environment variables or server-side proxy
2. **HTTPS**: Ensure your server uses HTTPS
3. **Domain Whitelist**: Restrict API access to your domains
4. **Rate Limiting**: Implement rate limiting to prevent abuse
5. **Error Monitoring**: Add error tracking for production issues

## 📞 Support

If you need help integrating the chat widget:

1. Check the troubleshooting section above
2. Test your API endpoints using the API test sheet
3. Verify your agent is working in the main interface first

## 🎯 Example Use Cases

- **Customer Support**: Add AI support to your website
- **Lead Generation**: Qualify leads with AI conversations  
- **Product Help**: Provide instant product assistance
- **FAQ Bot**: Answer common questions automatically
- **Booking Assistant**: Help users schedule appointments
- **E-commerce**: Assist with product recommendations

Happy chatting! 🚀
