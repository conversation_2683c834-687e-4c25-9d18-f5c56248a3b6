import { useState, useRef, useEffect } from 'react';
import { Icons } from '../icons';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card } from '../ui/card';
import { Avatar } from '../ui/avatar';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChatWidgetProps {
  agentId: string;
  apiKey: string;
  serverUrl?: string;
  title?: string;
  subtitle?: string;
  placeholder?: string;
  position?: 'bottom-right' | 'bottom-left';
  primaryColor?: string;
  minimized?: boolean;
  onToggle?: (minimized: boolean) => void;
}

export function ChatWidget({
  agentId,
  apiKey,
  serverUrl = 'http://localhost:9094',
  title = 'AI Assistant',
  subtitle = 'How can I help you today?',
  placeholder = 'Type your message...',
  position = 'bottom-right',
  primaryColor = '#3b82f6',
  minimized: controlledMinimized,
  onToggle,
}: ChatWidgetProps) {
  const [internalMinimized, setInternalMinimized] = useState(true);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const minimized = controlledMinimized ?? internalMinimized;
  const setMinimized = onToggle ?? setInternalMinimized;

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async () => {
    if (!inputValue.trim() || loading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${serverUrl}/api/v1/agents/${agentId}/chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          sessionId: `widget-${Date.now()}`,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const data = await response.json();
      
      // Extract the assistant's response from the API response
      let assistantContent = 'I received your message but couldn\'t generate a response.';
      
      if (data.response && data.response.messages) {
        const lastMessage = data.response.messages[data.response.messages.length - 1];
        if (lastMessage && lastMessage.content) {
          assistantContent = lastMessage.content;
        }
      } else if (data.response && typeof data.response === 'string') {
        assistantContent = data.response;
      }

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: assistantContent,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
      console.error('Chat widget error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
  };

  if (minimized) {
    return (
      <div className={`fixed ${positionClasses[position]} z-50`}>
        <Button
          onClick={() => setMinimized(false)}
          className="rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-200"
          style={{ backgroundColor: primaryColor }}
        >
          <Icons.messageCircle className="w-6 h-6 text-white" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`fixed ${positionClasses[position]} z-50 w-80 h-96`}>
      <Card className="w-full h-full flex flex-col shadow-2xl border-0 rounded-lg overflow-hidden">
        {/* Header */}
        <div 
          className="p-4 text-white flex items-center justify-between"
          style={{ backgroundColor: primaryColor }}
        >
          <div className="flex items-center space-x-3">
            <Avatar className="w-8 h-8">
              <div className="w-full h-full bg-white/20 flex items-center justify-center">
                <Icons.bot className="w-4 h-4 text-white" />
              </div>
            </Avatar>
            <div>
              <h3 className="font-medium text-sm">{title}</h3>
              <p className="text-xs opacity-90">{subtitle}</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setMinimized(true)}
            className="text-white hover:bg-white/20 p-1 h-auto"
          >
            <Icons.x className="w-4 h-4" />
          </Button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
          {messages.length === 0 && (
            <div className="text-center text-gray-500 text-sm py-8">
              <Icons.messageCircle className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>Start a conversation!</p>
            </div>
          )}
          
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-3 rounded-lg text-sm ${
                  message.role === 'user'
                    ? 'text-white rounded-br-none'
                    : 'bg-white border rounded-bl-none text-gray-800'
                }`}
                style={message.role === 'user' ? { backgroundColor: primaryColor } : {}}
              >
                <p className="whitespace-pre-wrap">{message.content}</p>
                <p className={`text-xs mt-1 opacity-70 ${
                  message.role === 'user' ? 'text-white' : 'text-gray-500'
                }`}>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
          ))}
          
          {loading && (
            <div className="flex justify-start">
              <div className="bg-white border rounded-lg rounded-bl-none p-3 text-sm">
                <div className="flex items-center space-x-2">
                  <Icons.spinner className="w-4 h-4 animate-spin" />
                  <span className="text-gray-500">Typing...</span>
                </div>
              </div>
            </div>
          )}
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-sm text-red-600">
              <p className="font-medium">Error:</p>
              <p>{error}</p>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="p-4 border-t bg-white">
          <div className="flex space-x-2">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={placeholder}
              disabled={loading}
              className="flex-1 text-sm"
            />
            <Button
              onClick={sendMessage}
              disabled={loading || !inputValue.trim()}
              size="sm"
              style={{ backgroundColor: primaryColor }}
              className="text-white hover:opacity-90"
            >
              <Icons.send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
