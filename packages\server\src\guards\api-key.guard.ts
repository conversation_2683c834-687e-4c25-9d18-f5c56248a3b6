import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AgentApiKeysService } from '../modules/core/agents/agent-api-keys.service';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(
    private agentApiKeysService: AgentApiKeysService,
    private reflector: Reflector
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if the endpoint is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      console.log('🔓 Public endpoint detected, skipping API key validation');
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const apiKey = this.extractApiKey(request);

    console.log('🛡️ API Key Guard activated');
    console.log('🛡️ Request URL:', request.url);
    console.log('🛡️ Request method:', request.method);
    console.log('🛡️ Authorization header:', request.headers.authorization ? 'Present' : 'Missing');

    if (!apiKey) {
      console.log('❌ No API key found in request');
      throw new UnauthorizedException('API key is required');
    }

    console.log('🔑 API key extracted:', apiKey.substring(0, 8) + '...');

    try {
      const apiKeyContext = await this.agentApiKeysService.validateApiKey(apiKey);

      console.log('✅ API key validation successful');
      console.log('✅ Agent ID:', apiKeyContext.agentId);
      console.log('✅ Workspace ID:', apiKeyContext.workspaceId);

      // Add user context to request
      request.user = {
        email: 'api-key-user',
        userId: 'api-key-user',
        workspaceId: apiKeyContext.workspaceId,
        workspaceUserId: 'api-key-user',
        roles: ['MAINTAINER'],
        apiKeyId: apiKeyContext.apiKeyId,
        agentId: apiKeyContext.agentId,
        permissions: apiKeyContext.permissions as Record<string, any>,
        isApiKeyAuth: true,
      };

      console.log('✅ User context set:', {
        agentId: request.user.agentId,
        workspaceId: request.user.workspaceId,
        isApiKeyAuth: request.user.isApiKeyAuth,
      });

      return true;
    } catch (error) {
      console.log('❌ API key validation failed:', error.message);
      throw new UnauthorizedException('Invalid API key');
    }
  }

  private extractApiKey(request: any): string | null {
    // Check for API key in different locations
    // 1. Authorization header with Bearer token
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // 2. X-API-Key header
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // 3. Query parameter
    const apiKeyQuery = request.query.api_key;
    if (apiKeyQuery) {
      return apiKeyQuery;
    }

    return null;
  }
}
