// Debug script to test API key authentication
const fetch = require('node-fetch');

const SERVER_URL = 'http://localhost:9094';

async function testApiKey() {
  console.log('🧪 Testing API Key Authentication');
  console.log('================================');
  
  // Test 1: Test endpoint without API key (should work because it's @Public())
  console.log('\n1. Testing public test endpoint without API key...');
  try {
    const response = await fetch(`${SERVER_URL}/api/v1/agents/test`);
    console.log('Status:', response.status);
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Public endpoint works:', data);
    } else {
      console.log('❌ Public endpoint failed:', await response.text());
    }
  } catch (error) {
    console.log('❌ Connection error:', error.message);
    return;
  }

  // Test 2: Test endpoint with API key
  console.log('\n2. Testing with API key...');
  const apiKey = process.argv[2]; // Pass API key as command line argument
  
  if (!apiKey) {
    console.log('❌ Please provide an API key as argument: node debug-api-key.js sk-XXXXXXXXXX');
    return;
  }
  
  console.log('API Key:', apiKey.substring(0, 8) + '...');
  console.log('API Key length:', apiKey.length);
  console.log('API Key format:', apiKey.startsWith('sk-') ? '✅ Valid' : '❌ Invalid');
  
  try {
    const response = await fetch(`${SERVER_URL}/api/v1/agents/test`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Status:', response.status);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API key authentication successful:', data);
    } else {
      const errorText = await response.text();
      console.log('❌ API key authentication failed:', errorText);
    }
  } catch (error) {
    console.log('❌ Request error:', error.message);
  }

  // Test 3: Test specific agent endpoint
  const agentId = process.argv[3]; // Pass agent ID as second argument
  if (agentId) {
    console.log('\n3. Testing agent-specific endpoint...');
    console.log('Agent ID:', agentId);
    
    try {
      const response = await fetch(`${SERVER_URL}/api/v1/agents/${agentId}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Agent endpoint works:', data);
      } else {
        const errorText = await response.text();
        console.log('❌ Agent endpoint failed:', errorText);
      }
    } catch (error) {
      console.log('❌ Agent request error:', error.message);
    }
  }
}

testApiKey();
